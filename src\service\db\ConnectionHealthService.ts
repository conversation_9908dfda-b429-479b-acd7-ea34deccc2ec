import { ConnectionPool } from './ConnectionPool';
import LoggerCenter from '../../logger/LoggerCenter';
import path from 'path';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export class ConnectionHealthService {
  private static instance: ConnectionHealthService;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private readonly checkIntervalMs = 60000; // 1 minute

  private constructor() {}

  public static getInstance(): ConnectionHealthService {
    if (!ConnectionHealthService.instance) {
      ConnectionHealthService.instance = new ConnectionHealthService();
    }
    return ConnectionHealthService.instance;
  }

  public startHealthChecks(): void {
    if (this.healthCheckInterval) {
      return;
    }
    
    logger.info('Starting database connection health checks');
    
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, this.checkIntervalMs);
  }

  public stopHealthChecks(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      logger.info('Database connection health checks stopped');
    }
  }

  private async performHealthCheck(): Promise<void> {
    const pool = ConnectionPool.getInstance().getPool();
    const client = await pool.connect();
    
    try {
      const startTime = Date.now();
      await client.query('SELECT 1');
      const duration = Date.now() - startTime;
      
      logger.debug(`Health check completed in ${duration}ms`);
    } catch (error) {
      logger.error(`Health check failed: ${error.message}`);
    } finally {
      client.release();
    }
  }
}