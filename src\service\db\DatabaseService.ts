import { ConnectionPool } from './ConnectionPool';
import { PoolClient } from 'pg';
import LoggerCenter from '../../logger/LoggerCenter';
import path from 'path';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export async function executeQuery<T>(
  queryFn: (client: PoolClient) => Promise<T>
): Promise<T> {
  const pool = ConnectionPool.getInstance().getPool();
  const client = await pool.connect();
  
  try {
    return await queryFn(client);
  } finally {
    // Always release the client back to the pool
    client.release();
    logger.debug('Database connection released back to the pool');
  }
}