#!/usr/bin/env node
import * as dotenv from 'dotenv';
import { App } from './app';
import SqController from './controller/SqController';
import ServiceCenter from './service/ServiceCenter';
import DBFileController from './controller/DBFileController';
import DocuController from './controller/DocuController';
import MultiFileDownloadController from './controller/MultiFileDownloadController';
import HealthCheckController from './controller/HealthCheckController';
import MStartServletController from './controller/MStartServletController';
import MKeyServletController from './controller/MKeyServletController';
import MKeepAliveServletController from './controller/MKeepAliveServletController';
import MDocuServlet2Controller from './controller/MDocuServlet2Controller';
import EMailController from './controller/EMailController';
import AuthCallbackController from './controller/AuthCallbackController';

import { ConnectionPool } from './service/db/ConnectionPool';

dotenv.config();

const startServer = async () => {
  console.warn('\n***************************');
  console.warn('Welcome to ESP Node Backend');
  console.warn('***************************\n');

  // Initialize connection pool early
  ConnectionPool.getInstance();

  const serverPort = +(process.env.PORT || '3001');

  const sc = ServiceCenter.getInstance();

  const sqlDir = `${__dirname}/service/sql`;
  const sqlDirectories = [`${sqlDir}/sql-java`, `${sqlDir}/sql-esp-java`, `${sqlDir}/sql-node`];
  await sc.init({ sqlDirectories });

  // Create Web Controllers and App
  const sqController = new SqController();
  const dbFileController = new DBFileController();
  const docuController = new DocuController();
  const healthCheckController = new HealthCheckController();
  const multiFileDownloadController = new MultiFileDownloadController();
  const mStartServletController = new MStartServletController();
  const mKeyServletController = new MKeyServletController();
  const mKeepAliveServletController = new MKeepAliveServletController();
  const mDocuServlet2Controller = new MDocuServlet2Controller();
  const emailController = new EMailController();
  const authCallbackController = new AuthCallbackController();

  const app = new App(
    [
      authCallbackController,
      sqController,
      dbFileController,
      docuController,
      healthCheckController,
      multiFileDownloadController,
      emailController,
      mStartServletController,
      mKeyServletController,
      mKeepAliveServletController,
      mDocuServlet2Controller
    ],
    serverPort
  );
  await app.init();
  app.listen();
};

startServer();
