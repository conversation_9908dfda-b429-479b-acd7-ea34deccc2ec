import { ConnectionPool } from './db/ConnectionPool';
import { executeTransaction } from './db/TransactionService';
import { executeMonitoredQuery } from './db/QueryMonitorService';
import LoggerCenter from '../logger/LoggerCenter';
import path from 'path';

const logger = LoggerCenter.getLogger(path.basename(__filename));

interface User {
  id: number;
  username: string;
  email: string;
  created_at: Date;
}

export class UserService {
  // Simple query using the pool directly
  public async getUserById(userId: number): Promise<User | null> {
    try {
      const users = await executeMonitoredQuery<User>(
        'SELECT * FROM users WHERE id = $1',
        [userId]
      );
      
      return users.length > 0 ? users[0] : null;
    } catch (error) {
      logger.error(`Error fetching user ${userId}: ${error.message}`);
      throw error;
    }
  }
  
  // Transaction example
  public async createUserWithProfile(username: string, email: string, profileData: any): Promise<User> {
    return executeTransaction(async (client) => {
      // Insert user
      const userResult = await client.query(
        'INSERT INTO users(username, email) VALUES($1, $2) RETURNING *',
        [username, email]
      );
      
      const user = userResult.rows[0];
      
      // Insert profile using the user ID
      await client.query(
        'INSERT INTO user_profiles(user_id, data) VALUES($1, $2)',
        [user.id, profileData]
      );
      
      return user;
    });
  }
  
  // Batch operation example
  public async batchUpdateUsers(updates: Array<{id: number, email: string}>): Promise<number> {
    const pool = ConnectionPool.getInstance().getPool();
    
    // Use a single client for multiple operations
    const client = await pool.connect();
    
    try {
      let updatedCount = 0;
      
      for (const update of updates) {
        const result = await client.query(
          'UPDATE users SET email = $1 WHERE id = $2',
          [update.email, update.id]
        );
        
        updatedCount += result.rowCount;
      }
      
      return updatedCount;
    } finally {
      client.release();
    }
  }
}