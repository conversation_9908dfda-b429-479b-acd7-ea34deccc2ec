import { ConnectionPool } from './ConnectionPool';
import LoggerCenter from '../../logger/LoggerCenter';
import path from 'path';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export async function executeSimpleQuery<T>(
  sql: string, 
  params: any[] = []
): Promise<T[]> {
  const pool = ConnectionPool.getInstance().getPool();
  
  try {
    const result = await pool.query(sql, params);
    return result.rows;
  } catch (error) {
    logger.error(`Query execution error: ${error.message}`);
    throw error;
  }
}