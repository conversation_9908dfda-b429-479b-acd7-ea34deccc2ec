import { ConnectionPool } from './ConnectionPool';
import { executeQuery } from './DatabaseService';
import LoggerCenter from '../../logger/LoggerCenter';
import path from 'path';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export async function executeTransaction<T>(
  transactionFn: (client: any) => Promise<T>
): Promise<T> {
  return executeQuery(async (client) => {
    try {
      await client.query('BEGIN');
      const result = await transactionFn(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error(`Transaction failed: ${error.message}`);
      throw error;
    }
  });
}