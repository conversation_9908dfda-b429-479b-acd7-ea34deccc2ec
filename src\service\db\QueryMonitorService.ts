import { ConnectionPool } from './ConnectionPool';
import { executeQuery } from './DatabaseService';
import LoggerCenter from '../../logger/LoggerCenter';
import path from 'path';

const logger = LoggerCenter.getLogger(path.basename(__filename));

interface QueryStats {
  query: string;
  duration: number;
  timestamp: Date;
  parameters?: any[];
}

export class QueryMonitorService {
  private static instance: QueryMonitorService;
  private queryStats: QueryStats[] = [];
  private readonly maxQueryHistory = 100;

  private constructor() {}

  public static getInstance(): QueryMonitorService {
    if (!QueryMonitorService.instance) {
      QueryMonitorService.instance = new QueryMonitorService();
    }
    return QueryMonitorService.instance;
  }

  public recordQuery(query: string, parameters: any[], duration: number): void {
    this.queryStats.unshift({
      query,
      parameters,
      duration,
      timestamp: new Date()
    });
    
    // Keep only the most recent queries
    if (this.queryStats.length > this.maxQueryHistory) {
      this.queryStats.pop();
    }
    
    // Log slow queries
    if (duration > 1000) { // More than 1 second
      logger.warn(`Slow query detected (${duration}ms): ${query}`);
    }
  }

  public getQueryStats(): QueryStats[] {
    return [...this.queryStats];
  }

  public async getPoolStatus(): Promise<any> {
    const pool = ConnectionPool.getInstance().getPool();
    
    return {
      totalCount: pool.totalCount,
      idleCount: pool.idleCount,
      waitingCount: pool.waitingCount
    };
  }
}

// Wrapper function to monitor query performance
export async function executeMonitoredQuery<T>(
  sql: string,
  params: any[] = []
): Promise<T[]> {
  const startTime = Date.now();
  const pool = ConnectionPool.getInstance().getPool();
  
  try {
    const result = await pool.query(sql, params);
    const duration = Date.now() - startTime;
    
    // Record query stats
    QueryMonitorService.getInstance().recordQuery(sql, params, duration);
    
    return result.rows;
  } catch (error) {
    const duration = Date.now() - startTime;
    logger.error(`Query failed after ${duration}ms: ${error.message}`);
    throw error;
  }
}