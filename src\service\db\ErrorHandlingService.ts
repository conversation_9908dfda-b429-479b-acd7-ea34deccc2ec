import { ConnectionPool } from './ConnectionPool';
import { DatabaseError } from 'pg';
import LoggerCenter from '../../logger/LoggerCenter';
import path from 'path';

const logger = LoggerCenter.getLogger(path.basename(__filename));

// Custom error class for database errors
export class DatabaseServiceError extends Error {
  constructor(message: string, public originalError?: Error) {
    super(message);
    this.name = 'DatabaseServiceError';
  }
}

export async function executeQueryWithErrorHandling<T>(
  sql: string,
  params: any[] = []
): Promise<T[]> {
  const pool = ConnectionPool.getInstance().getPool();
  
  try {
    const result = await pool.query(sql, params);
    return result.rows;
  } catch (error) {
    // Handle specific PostgreSQL errors
    if (error instanceof DatabaseError) {
      switch (error.code) {
        case '23505': // unique_violation
          throw new DatabaseServiceError('A record with this data already exists', error);
        case '23503': // foreign_key_violation
          throw new DatabaseServiceError('Referenced record does not exist', error);
        case '42P01': // undefined_table
          throw new DatabaseServiceError('Table does not exist', error);
        case '57014': // query_canceled
          throw new DatabaseServiceError('Query timed out', error);
        default:
          logger.error(`Database error: ${error.code} - ${error.message}`);
          throw new DatabaseServiceError('Database operation failed', error);
      }
    }
    
    // Handle other errors
    logger.error(`Unexpected error during query execution: ${error.message}`);
    throw new DatabaseServiceError('Unexpected database error', error);
  }
}