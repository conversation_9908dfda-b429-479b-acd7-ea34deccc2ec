import { Pool, PoolConfig } from 'pg';
import LoggerCenter from '../../logger/LoggerCenter';
import path from 'path';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export class ConnectionPool {
  private static instance: ConnectionPool;
  private pool: Pool;

  private constructor() {
    const poolConfig: PoolConfig = {
      user: process.env.DB_TRX_USER,
      password: process.env.DB_TRX_PASSWORD,
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT || '5432'),
      database: process.env.DB_DATABASE,

      // Pool configuration with defaults that can be overridden by environment variables
      max: parseInt(process.env.DB_POOL_MAX_CONNECTIONS || '20'),
      idleTimeoutMillis: parseInt(process.env.DB_POOL_IDLE_TIMEOUT || '30000'),
      connectionTimeoutMillis: parseInt(process.env.DB_POOL_CONNECTION_TIMEOUT || '2000'),

      // SSL configuration
      ssl: process.env.DB_SSL === 'true',

      // Query timeout (in milliseconds)
      statement_timeout: parseInt(process.env.DB_STATEMENT_TIMEOUT || '60000'),
    };

    this.pool = new Pool(poolConfig);

    // Log pool creation with configuration (excluding sensitive data)
    const logConfig = { ...poolConfig };
    delete logConfig.password;
    logger.info(`Database connection pool created with config: ${JSON.stringify(logConfig)}`);

    // Set up event handlers
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.pool.on('error', (err, client) => {
      logger.error(`Unexpected error on idle client: ${err.message}`);
    });

    this.pool.on('connect', (client) => {
      logger.debug('New client connected to database');
    });

    this.pool.on('acquire', (client) => {
      logger.debug('Client acquired from pool');
    });

    this.pool.on('remove', (client) => {
      logger.debug('Client removed from pool');
    });
  }

  public static getInstance(): ConnectionPool {
    if (!ConnectionPool.instance) {
      ConnectionPool.instance = new ConnectionPool();
    }
    return ConnectionPool.instance;
  }

  public getPool(): Pool {
    return this.pool;
  }

  // Method to gracefully close the pool
  public async end(): Promise<void> {
    if (this.pool) {
      logger.info('Closing database connection pool');
      await this.pool.end();
      logger.info('Database connection pool closed');
    }
  }
}
